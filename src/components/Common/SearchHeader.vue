<template>
  <div class="search-header">
    <div class="search-input" @click="handleInputClick">
      <!-- 搜索图标插槽 -->
      <slot name="search-icon">
        <img src="@/static/images/search.png" alt="搜索" class="search-icon" />
      </slot>
      <input
        type="text"
        v-model="keyword"
        :placeholder="placeholder"
        @keyup.enter="handleSearch"
        :readonly="redirectToSearch"
        ref="inputRef"
      />
      <div class="search-btn" @click.stop="handleSearch">搜索</div>
    </div>
    <!-- 右侧操作区插槽 -->
    <slot name="right-action">
      <!-- 默认内容为空 -->
    </slot>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '搜索'
  },
  redirectToSearch: {
    type: Boolean,
    default: false
  },
  redirectUrl: {
    type: String,
    default: '/search'
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'clickable'])

const keyword = ref(props.modelValue)
const inputRef = ref(null)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  keyword.value = newVal
})

// 监听内部值变化
watch(keyword, (newVal) => {
  emit('update:modelValue', newVal)
})

// 处理输入框点击事件
const handleInputClick = () => {
  if (props.redirectToSearch) {
    // 如果需要跳转，则跳转到指定页面
    router.push(props.redirectUrl)
  } else {
    // 如果不需要跳转，则聚焦输入框
    inputRef.value?.focus()
  }
  // 触发点击事件，供外部监听
  emit('clickable')
}

// 搜索处理函数
const handleSearch = () => {
  emit('search', keyword.value)
}


defineExpose({ inputRef })

</script>

<style scoped lang="less">
.search-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #fff;
  box-sizing: border-box;
}

.search-input {
  flex: 1;
  display: flex;
  align-items: center;
  height: 32px;
  background-color: @bg-color-white;
  border-radius: 30px;
  padding: 3px 3px 3px 11px;
  box-sizing: border-box;
  border: 1px solid rgba(226,232,238,1);

  .search-icon {
    width: 13px;
    height: 13px;
    margin-right: 6px;
  }

  input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: @font-size-13;
    color: @text-color-primary;
    outline: none;

    &::placeholder {
      color: @text-color-tertiary;
    }
  }

  .search-btn {
    width: 50px;
    height: 26px;
    background-image: @gradient-orange-115;
    border-radius: @radius-15;
    font-size: @font-size-13;
    font-weight: @font-weight-500;
    text-align: center;
    line-height: 26px;
    color: @text-color-white;
  }
}
</style>
